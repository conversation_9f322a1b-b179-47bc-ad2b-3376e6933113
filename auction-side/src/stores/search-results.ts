import {defineStore} from 'pinia'
import {reactive, ref} from 'vue'
import type {RouteLocationNormalizedLoaded, Router} from 'vue-router'
import {formatDateString, priceLocaleString} from '../composables/common'
import {CLASSIFICATION_TYPES} from '../constants/classification'
import {noImg, PATH_NAME} from '../defined/const'

// Type definitions for API responses and data structures
type BidStatus = {
  current_price: number
  bid_price: number
  bid_quantity: number
  tax_rate: number
  pitch_width?: number
  is_top_member?: boolean | null
}

type AttentionInfo = {
  bid_count: number
  favorited_count?: number
}

type FreeField = {
  productName: string
  image_url?: string
  start_price?: number
  category?: string
}

type BidHistory = {
  bid_price: number
  bid_quantity: number
  bid_datetime: string
  user_id?: string
}

type RawAuctionItem = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories?: BidHistory[]
}

type FormattedAuctionItem = RawAuctionItem & {
  link: string
  currentPrice: string
  currentPriceTaxIncluded: string
  noOfBids: number
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  bidPrice: string
  bidQuantity: string
  bidInputError: {
    bidPrice: string | null
    bidQuantity: string | null
  }
}

type ExhibitionGroup = {
  exhibition_no: string
  exhibition_name: string
  start_datetime: string
  end_datetime: string
}

type ProductList = {
  all: FormattedAuctionItem[]
  exhibitionList: ExhibitionGroup[]
}

type ProductDetails = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories: BidHistory[]
  favorite_count: number
  currentPrice: string
  currentPriceTaxIncluded: string
  bid_count: number
  endDatePart: string
  endTimePart: string
  images: string[]
  freeFields: FreeField
  productName: string
}

// Removed unused types WebSocketMessage and ContactFormData

export const useSearchResultStore = defineStore('search-results', () => {
  const showCountConstant = 20

  const searchKeyTop = ref<string>('')
  const searchKeyTopAfter = ref<string>('')
  const searchKey = ref<string | null>(null)
  const unSoldOut = ref<boolean>(false)
  const favorite = ref<boolean | null>(null)
  const viewMore = ref<number>(1)
  const modelList = reactive<string[]>([])
  const categoryList = reactive<string[]>([])
  const brandList = reactive<string[]>([])
  const showCount = ref<number>(showCountConstant)
  const totalCount = ref<number>(0)
  const totalHistoryCount = ref<number>(0)
  const sorter = ref<string | null>(null)
  const startPrice = ref<string | null>(null)
  const endPrice = ref<string | null>(null)
  const myPageSelectedClassification = ref<string>(
    CLASSIFICATION_TYPES.ASCENDING
  ) // sealed or ascending

  const searchCategory = ref<string | null>(null)
  const searchBrand = ref<string | null>(null)

  const productDetails = reactive<Partial<ProductDetails>>({})
  const productDetailsForContact = reactive<Partial<ProductDetails>>({})
  const productList = reactive<ProductList>({exhibitionList: [], all: []})

  const constants = ref<any[]>([])

  const panelView = ref<number>(0)

  const updateFavoriteCount = (
    exhibitionItemNo: string,
    isIncrease: boolean,
    router: Router
  ) => {
    console.log('updateFavoriteCount')
    const foundItem = productList.all.find(
      x => x.exhibition_item_no === exhibitionItemNo
    )
    if (foundItem) {
      foundItem.attention_info.favorited_count += isIncrease ? 1 : -1
      if (router.currentRoute.value.path.includes(PATH_NAME.DETAIL)) {
        productDetails.favorite_count += isIncrease ? 1 : -1
      }
    }
  }

  // trigger from websocket when item changed
  const setChanged = (
    {
      attention_info,
      bid_status,
      bid_histories,
      exhibition_item_no,
      end_datetime,
    }: {
      attention_info: AttentionInfo
      bid_status: BidStatus
      bid_histories: BidHistory[]
      exhibition_item_no: string
      end_datetime?: string
    },
    route: RouteLocationNormalizedLoaded
  ) => {
    const foundItemIndex = productList.all.findIndex(
      item => String(item.exhibition_item_no) === String(exhibition_item_no)
    )
    if (foundItemIndex !== -1) {
      const updatedItem = {
        ...productList.all[foundItemIndex],
        attention_info,
        bid_status: {
          ...productList.all[foundItemIndex].bid_status,
          ...bid_status,
        },
        currentPrice: bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          (bid_status.current_price || 0) *
            ((1 + (bid_status.tax_rate || 0)) / 100)
        ).toLocaleString(),
        endDatePart: end_datetime
          ? (formatDateString(end_datetime)?.datePart ?? 'MM/DD')
          : 'MM/DD',
        endTimePart: end_datetime
          ? (formatDateString(end_datetime)?.timePart ?? 'HH:mm')
          : 'HH:mm',
      }

      productList.all.splice(foundItemIndex, 1, updatedItem)
    }
    // console.log('setChanged-->route: ', {route})
    // Handle updates to `productDetails` if standing on the detail page
    if (route.path.includes(PATH_NAME.DETAIL)) {
      if (
        exhibition_item_no &&
        String(productDetails.exhibition_item_no) === String(exhibition_item_no)
      ) {
        productDetails.bid_histories = bid_histories
        productDetails.bid_status = bid_status
        productDetails.currentPrice =
          bid_status.current_price?.toLocaleString() || '0'
        productDetails.currentPriceTaxIncluded = Math.round(
          (bid_status.current_price || 0) *
            (1 + (bid_status.tax_rate || 0) / 100)
        ).toLocaleString()

        productDetails.bid_count = attention_info.bid_count

        if (end_datetime) {
          const extended = formatDateString(end_datetime)
          productDetails.endDatePart = extended.datePart ?? 'MM/DD'
          productDetails.endTimePart = extended.timePart ?? 'HH:mm'
        }
      }
    }
  }

  // Set product when get data from api [public/refresh-item]
  const setProductDetails = (searchResult: any) => {
    console.log('searchResult(検索結果): ', searchResult)
    const result = {
      ...searchResult,
      exhibition_item_no: searchResult.exhibition_item_no,
      productName: searchResult?.free_fields?.[0].productName,
      freeFields: searchResult?.free_fields?.[0],
      images: [searchResult?.free_fields?.[0].image_url || noImg],
      startPrice: searchResult?.free_fields?.[0]?.start_price?.toLocaleString(),
      currentPrice: searchResult?.bid_status?.current_price?.toLocaleString(),
      currentPriceTaxIncluded: Math.round(
        searchResult?.bid_status?.current_price *
          (1 + searchResult?.bid_status?.tax_rate / 100)
      )?.toLocaleString(),
      endDatePart:
        formatDateString(searchResult?.bid_status?.end_datetime)?.datePart ??
        'MM/DD',
      endTimePart:
        formatDateString(searchResult?.bid_status?.end_datetime)?.timePart ??
        'HH:mm',
      startDatePart:
        formatDateString(searchResult?.bid_status?.start_datetime)?.datePart ??
        'MM/DD',
      startTimePart:
        formatDateString(searchResult?.bid_status?.start_datetime)?.timePart ??
        'HH:mm',
      preview_end_datetime: searchResult.preview_end_datetime,
      favorite_count: searchResult.attention_info.favorited_count,
      bid_count: searchResult.attention_info.bid_count,
      view_count: searchResult.attention_info.view_count,
      basic_category: searchResult?.free_fields?.[0].basic_category,
      categoryCode: searchResult?.category_code,
      category: searchResult?.category,
      brand: searchResult?.free_fields?.[0].brand,
      itemNo: searchResult?.item_no,
      sold_out: searchResult?.sold_out === 1,
      auction_classification: searchResult?.auction_classification, // オークション方式(1:せり上げ、2:封印入札)
      postage: searchResult.postage
        ? Math.round(
            Number(searchResult?.postage) *
              Number(searchResult?.free_fields?.[0].quantity)
          )?.toLocaleString()
        : null,
    }
    if (
      searchResult?.free_fields?.[0].multi_images &&
      searchResult?.free_fields?.[0].multi_images.length > 0
    ) {
      const multiImages = searchResult?.free_fields?.[0].multi_images
      if (searchResult?.free_fields?.[0].image_url) {
        result.images.push(...multiImages)
      } else {
        // 基本画像がない場合はnoImg上書き
        result.images = multiImages
      }
    }
    Object.assign(productDetails, result)
  }

  // Trigger when Detail page mount or getContact button clicked
  const setProductListForContact = () => {
    const p = {
      image: productDetails?.images?.[0],
      sold_out: false,
      bid_status: {...productDetails.bid_status, is_top_member: null},
      free_field: productDetails.freeFields,
      category_id: productDetails.freeFields.category,
      attention_info: Object.assign({}, productDetails?.attention_info || {}),
      exhibition_item_no: productDetails.exhibition_item_no,
      category: productDetails.freeFields.category,
      link: '',
      productName: productDetails.productName,
      imgSrc: productDetails.images?.[0] ?? '',
      currentPrice: productDetails.currentPrice,
      currentPriceTaxIncluded: Math.round(
        productDetails.bid_status.current_price *
          (1 + productDetails.bid_status.tax_rate / 100)
      )?.toLocaleString(),
      noOfBids: productDetails.bid_count,
      endDatePart: productDetails.endDatePart,
      endTimePart: productDetails.endTimePart,
    }
    Object.assign(productDetailsForContact, p)
  }

  // Set when handle search -> [public/search-auction-items]
  const setProductList = (searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `${PATH_NAME.DETAIL}/${item.exhibition_item_no}`,
        productName: item.free_field.productName,
        imgSrc: item.free_field.image_url ?? noImg,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
        bidPrice: priceLocaleString(item.bid_status.bid_price, 10), // input bid price value on screen will be saved here
        bidQuantity: priceLocaleString(item.bid_status.bid_quantity, 10), // input bid quantity
        bidInputError: {
          bidPrice: null,
          bidQuantity: null,
        }, // error message for bid price and quantity
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }

  // unused method
  const setProductListByCategory = (_categoryId: string, searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        category: item.category_id,
        itemNo: item.item_no,
        link: `/details/${item.exhibition_item_no}`,
        productName: item.free_field.productName,
        imgSrc: item.free_field.image_url ?? noImg,
        currentPrice: item.bid_status.current_price?.toLocaleString(),
        currentPriceTaxIncluded: Math.round(
          item.bid_status.current_price +
            item.bid_status.current_price * (item.bid_status.tax_rate / 100)
        )?.toLocaleString(),
        noOfBids: item.attention_info.bid_count,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }

  // Trigger when fetch api [public/get-all-successful-bid-history]
  const setBidHistory = (searchResult: any) => {
    const itemList = searchResult?.items ?? []
    totalCount.value = searchResult?.count ?? 0
    searchKeyTopAfter.value = searchKeyTop.value
    const formattedItems = itemList?.map((item: RawAuctionItem) => {
      const {datePart: endDatePart, timePart: endTimePart} = formatDateString(
        item.end_datetime
      )
      const {datePart: startDatePart, timePart: startTimePart} =
        formatDateString(item.start_datetime)
      return {
        ...item,
        link: `/details/${item.exhibition_item_no}`,
        endDatePart,
        endTimePart,
        startDatePart,
        startTimePart,
      }
    })
    // Exhibition list
    productList.exhibitionList = searchResult?.exhibition_group ?? []
    // All items
    productList.all = formattedItems
  }

  const setSearchKeyTop = (key: string) => {
    searchKeyTop.value = key
  }

  const setConstants = (data: any[]) => {
    constants.value = data || []
  }

  const resetParams = () => {
    searchKeyTop.value = null
    searchKey.value = null
    searchCategory.value = null
    searchBrand.value = null
    modelList.length = 0
    categoryList.length = 0
    brandList.length = 0
    unSoldOut.value = false
    favorite.value = null
    showCount.value = showCountConstant
    panelView.value = 0
    viewMore.value = 1
    sorter.value = null
    startPrice.value = null
    endPrice.value = null
  }

  const resetProductList = () => {
    productList.exhibitionList = []
    productList.all = []
    totalCount.value = 0
  }

  return {
    modelList,
    categoryList,
    brandList,
    unSoldOut,
    favorite,
    showCount,
    panelView,
    sorter,
    startPrice,
    endPrice,
    viewMore,
    totalCount,
    totalHistoryCount,
    searchKey,
    searchKeyTop,
    searchKeyTopAfter,
    searchCategory,
    searchBrand,
    productDetails,
    productDetailsForContact,
    productList,
    constants,
    setChanged,
    setProductDetails,
    setProductListForContact,
    setProductList,
    setSearchKeyTop,
    resetParams,
    resetProductList,
    setBidHistory,
    updateFavoriteCount,
    setProductListByCategory,
    setConstants,
    myPageSelectedClassification,
  }
})
